const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { body, validationResult } = require('express-validator');
const Hospital = require('../models/Hospital');
const Appointment = require('../models/Appointment');
const { isHospital, isAdmin } = require('../middleware/auth');

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/hospital_img/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + '-' + file.originalname);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5242880 // 5MB
    },
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|gif/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Only image files are allowed'));
        }
    }
});

// GET home page - list all verified hospitals
router.get('/', async (req, res) => {
    try {
        const hospitals = await Hospital.find({ verified: true }).select('-password');
        res.render('index', {
            title: 'All Hospitals',
            hospitals,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching hospitals:', error);
        req.flash('error', 'Error loading hospitals');
        res.render('index', {
            title: 'All Hospitals',
            hospitals: [],
            success: req.flash('success'),
            error: req.flash('error')
        });
    }
});

// GET hospital details page
router.get('/hospital/:id', async (req, res) => {
    try {
        const hospital = await Hospital.findById(req.params.id).select('-password');
        if (!hospital) {
            req.flash('error', 'Hospital not found');
            return res.redirect('/');
        }

        res.render('hospital/show', {
            title: hospital.hospital_name,
            hospital,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching hospital:', error);
        req.flash('error', 'Error loading hospital details');
        res.redirect('/');
    }
});

// GET new hospital registration page
router.get('/new', (req, res) => {
    res.render('hospital/new', {
        title: 'Add Your Hospital',
        success: req.flash('success'),
        error: req.flash('error')
    });
});

// POST new hospital registration
router.post('/new', upload.single('image'), [
    body('hospital_name').trim().isLength({ min: 2 }).withMessage('Hospital name must be at least 2 characters'),
    body('address').trim().isLength({ min: 5 }).withMessage('Address must be at least 5 characters'),
    body('zip_code').trim().isLength({ min: 5, max: 10 }).withMessage('Zip code must be between 5-10 characters'),
    body('contact_number').trim().isLength({ min: 10 }).withMessage('Contact number must be at least 10 digits'),
    body('email').isEmail().normalizeEmail().withMessage('Please enter a valid email'),
    body('receptionist_name').trim().isLength({ min: 2 }).withMessage('Receptionist name must be at least 2 characters'),
    body('receptionist_contact').trim().isLength({ min: 10 }).withMessage('Receptionist contact must be at least 10 digits'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            req.flash('error', errors.array()[0].msg);
            return res.redirect('/new');
        }

        const {
            hospital_name,
            address,
            zip_code,
            contact_number,
            email,
            receptionist_name,
            receptionist_contact,
            password
        } = req.body;

        // Check if hospital already exists
        const existingHospital = await Hospital.findOne({ email });
        if (existingHospital) {
            req.flash('error', 'Email already registered');
            return res.redirect('/new');
        }

        // Create new hospital
        const hospital = new Hospital({
            hospital_name,
            address,
            zip_code,
            contact_number,
            email,
            doctor_name: receptionist_name,
            doctor_contact_number: receptionist_contact,
            doctor_email: email,
            image: req.file ? req.file.path : null,
            password,
            verified: false // Will be verified by admin
        });

        await hospital.save();
        req.flash('success', 'Hospital registered successfully! Please wait for admin verification.');
        res.redirect('/');

    } catch (error) {
        console.error('Hospital registration error:', error);
        req.flash('error', 'An error occurred during registration');
        res.redirect('/new');
    }
});

// GET hospital profile page
router.get('/hospital/profile', isHospital, async (req, res) => {
    try {
        const hospital = await Hospital.findOne({ email: req.session.email }).select('-password');
        if (!hospital) {
            req.flash('error', 'Hospital not found');
            return res.redirect('/auth/logout');
        }

        res.render('hospital/profile', {
            title: 'Hospital Profile',
            hospital,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching hospital profile:', error);
        req.flash('error', 'Error loading profile');
        res.redirect('/');
    }
});

// GET hospital appointments
router.get('/hospital/appointments', isHospital, async (req, res) => {
    try {
        const hospital = await Hospital.findOne({ email: req.session.email });
        if (!hospital) {
            req.flash('error', 'Hospital not found');
            return res.redirect('/auth/logout');
        }

        const appointments = await Appointment.find({ hospital_id: hospital._id })
            .sort({ createdAt: -1 });

        res.render('hospital/appointments', {
            title: 'Patient Appointments',
            appointments,
            hospital,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching appointments:', error);
        req.flash('error', 'Error loading appointments');
        res.redirect('/hospital/profile');
    }
});

// GET admin page
router.get('/admin', isAdmin, async (req, res) => {
    try {
        const unverifiedHospitals = await Hospital.find({ verified: false }).select('-password');
        const verifiedHospitals = await Hospital.find({ verified: true }).select('-password');

        res.render('admin/dashboard', {
            title: 'Admin Dashboard',
            unverifiedHospitals,
            verifiedHospitals,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching admin data:', error);
        req.flash('error', 'Error loading admin dashboard');
        res.redirect('/');
    }
});

// POST verify hospital
router.post('/admin/verify/:id', isAdmin, async (req, res) => {
    try {
        const hospital = await Hospital.findById(req.params.id);
        if (!hospital) {
            req.flash('error', 'Hospital not found');
            return res.redirect('/admin');
        }

        hospital.verified = true;
        await hospital.save();

        req.flash('success', `${hospital.hospital_name} has been verified successfully`);
        res.redirect('/admin');
    } catch (error) {
        console.error('Error verifying hospital:', error);
        req.flash('error', 'Error verifying hospital');
        res.redirect('/admin');
    }
});

// POST unverify hospital
router.post('/admin/unverify/:id', isAdmin, async (req, res) => {
    try {
        const hospital = await Hospital.findById(req.params.id);
        if (!hospital) {
            req.flash('error', 'Hospital not found');
            return res.redirect('/admin');
        }

        hospital.verified = false;
        await hospital.save();

        req.flash('success', `${hospital.hospital_name} has been unverified`);
        res.redirect('/admin');
    } catch (error) {
        console.error('Error unverifying hospital:', error);
        req.flash('error', 'Error unverifying hospital');
        res.redirect('/admin');
    }
});

module.exports = router;
