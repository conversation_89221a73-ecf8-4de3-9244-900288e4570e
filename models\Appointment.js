const mongoose = require('mongoose');

const appointmentSchema = new mongoose.Schema({
    patient_name: {
        type: String,
        required: [true, 'Patient name is required'],
        trim: true,
        maxlength: [255, 'Patient name cannot exceed 255 characters']
    },
    age: {
        type: Number,
        required: [true, 'Age is required'],
        min: [0, 'Age cannot be negative'],
        max: [150, 'Age cannot exceed 150']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        trim: true,
        lowercase: true,
        maxlength: [255, 'Email cannot exceed 255 characters'],
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    contact_number: {
        type: String,
        required: [true, 'Contact number is required'],
        trim: true,
        maxlength: [20, 'Contact number cannot exceed 20 characters']
    },
    reason: {
        type: String,
        required: [true, 'Reason for appointment is required'],
        trim: true,
        maxlength: [1000, 'Reason cannot exceed 1000 characters']
    },
    token_number: {
        type: Number,
        required: [true, 'Token number is required']
    },
    hospital_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Hospital',
        required: [true, 'Hospital ID is required']
    },
    appointment_date: {
        type: Date,
        default: Date.now
    },
    status: {
        type: String,
        enum: {
            values: ['pending', 'confirmed', 'completed', 'cancelled'],
            message: 'Status must be pending, confirmed, completed, or cancelled'
        },
        default: 'pending'
    }
}, {
    timestamps: true
});

// Index for better query performance
appointmentSchema.index({ hospital_id: 1, appointment_date: -1 });
appointmentSchema.index({ email: 1, appointment_date: -1 });
appointmentSchema.index({ hospital_id: 1, token_number: 1 });

module.exports = mongoose.model('Appointment', appointmentSchema);
