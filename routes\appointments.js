const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const Hospital = require('../models/Hospital');
const Appointment = require('../models/Appointment');
const { isLoggedIn } = require('../middleware/auth');

// POST book appointment
router.post('/book/:hospitalId', isLoggedIn, [
    body('patient_name').trim().isLength({ min: 2 }).withMessage('Patient name must be at least 2 characters'),
    body('age').isInt({ min: 1, max: 150 }).withMessage('Age must be between 1 and 150'),
    body('contact_number').trim().isLength({ min: 10 }).withMessage('Contact number must be at least 10 digits'),
    body('reason').trim().isLength({ min: 5 }).withMessage('Reason must be at least 5 characters')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            req.flash('error', errors.array()[0].msg);
            return res.redirect(`/hospital/${req.params.hospitalId}`);
        }

        const hospital = await Hospital.findById(req.params.hospitalId);
        if (!hospital) {
            req.flash('error', 'Hospital not found');
            return res.redirect('/');
        }

        const { patient_name, age, contact_number, reason } = req.body;

        // Get next token number
        const nextToken = hospital.current_token + 1;

        // Create appointment
        const appointment = new Appointment({
            patient_name,
            age: parseInt(age),
            email: req.session.email,
            contact_number,
            reason,
            token_number: nextToken,
            hospital_id: hospital._id
        });

        await appointment.save();

        // Update hospital's current token
        hospital.current_token = nextToken;
        await hospital.save();

        req.flash('success', `Appointment booked successfully! Your token number is ${nextToken}`);
        res.redirect(`/hospital/${req.params.hospitalId}`);

    } catch (error) {
        console.error('Error booking appointment:', error);
        req.flash('error', 'Error booking appointment');
        res.redirect(`/hospital/${req.params.hospitalId}`);
    }
});

// POST update appointment status (for hospitals)
router.post('/update-status/:id', async (req, res) => {
    try {
        const { status } = req.body;
        const appointment = await Appointment.findById(req.params.id);

        if (!appointment) {
            req.flash('error', 'Appointment not found');
            return res.redirect('/hospital/appointments');
        }

        // Check if user is authorized to update this appointment
        if (req.session.user_role === 'hospital') {
            const hospital = await Hospital.findOne({ email: req.session.email });
            if (!hospital || !appointment.hospital_id.equals(hospital._id)) {
                req.flash('error', 'Unauthorized to update this appointment');
                return res.redirect('/hospital/appointments');
            }
        }

        appointment.status = status;
        await appointment.save();

        req.flash('success', 'Appointment status updated successfully');
        res.redirect('/hospital/appointments');

    } catch (error) {
        console.error('Error updating appointment status:', error);
        req.flash('error', 'Error updating appointment status');
        res.redirect('/hospital/appointments');
    }
});

// POST cancel appointment (for patients)
router.post('/cancel/:id', isLoggedIn, async (req, res) => {
    try {
        const appointment = await Appointment.findById(req.params.id);

        if (!appointment) {
            req.flash('error', 'Appointment not found');
            return res.redirect('/patients/appointments');
        }

        // Check if user is authorized to cancel this appointment
        if (appointment.email !== req.session.email) {
            req.flash('error', 'Unauthorized to cancel this appointment');
            return res.redirect('/patients/appointments');
        }

        appointment.status = 'cancelled';
        await appointment.save();

        req.flash('success', 'Appointment cancelled successfully');
        res.redirect('/patients/appointments');

    } catch (error) {
        console.error('Error cancelling appointment:', error);
        req.flash('error', 'Error cancelling appointment');
        res.redirect('/patients/appointments');
    }
});

module.exports = router;
