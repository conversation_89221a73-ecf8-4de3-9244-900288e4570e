const mongoose = require('mongoose');

const patientReportSchema = new mongoose.Schema({
    patient_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Patient',
        required: [true, 'Patient ID is required']
    },
    report_file: {
        type: String,
        required: [true, 'Report file is required'],
        trim: true
    },
    report_date: {
        type: Date,
        required: [true, 'Report date is required'],
        default: Date.now
    },
    report_title: {
        type: String,
        trim: true,
        maxlength: [255, 'Report title cannot exceed 255 characters']
    },
    report_description: {
        type: String,
        trim: true,
        maxlength: [1000, 'Report description cannot exceed 1000 characters']
    }
}, {
    timestamps: true
});

// Index for better query performance
patientReportSchema.index({ patient_id: 1, report_date: -1 });

module.exports = mongoose.model('PatientReport', patientReportSchema);
