const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const Hospital = require('../models/Hospital');
const Patient = require('../models/Patient');
const { redirectIfLoggedIn } = require('../middleware/auth');

// GET login page
router.get('/login', redirectIfLoggedIn, (req, res) => {
    res.render('auth/login', {
        title: 'Login',
        error: req.flash('error'),
        success: req.flash('success')
    });
});

// POST login
router.post('/login', [
    body('email').isEmail().normalizeEmail().withMessage('Please enter a valid email'),
    body('password').notEmpty().withMessage('Password is required'),
    body('role').isIn(['hospital', 'patient']).withMessage('Please select a valid role')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            req.flash('error', errors.array()[0].msg);
            return res.redirect('/auth/login');
        }

        const { email, password, role } = req.body;
        let user;

        if (role === 'hospital') {
            user = await Hospital.findOne({ email });
        } else {
            user = await Patient.findOne({ email });
        }

        if (!user) {
            req.flash('error', 'Invalid email or password');
            return res.redirect('/auth/login');
        }

        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            req.flash('error', 'Invalid email or password');
            return res.redirect('/auth/login');
        }

        // Set session
        req.session.isLoggedIn = true;
        req.session.user_role = role;
        req.session.email = email;
        req.session.user = user;

        req.flash('success', 'Login successful!');

        // Redirect based on role
        if (role === 'hospital') {
            res.redirect('/hospital/profile');
        } else {
            res.redirect('/patients/profile');
        }

    } catch (error) {
        console.error('Login error:', error);
        req.flash('error', 'An error occurred during login');
        res.redirect('/auth/login');
    }
});

// GET signup page
router.get('/signup', redirectIfLoggedIn, (req, res) => {
    res.render('auth/signup', {
        title: 'Sign Up',
        error: req.flash('error'),
        success: req.flash('success')
    });
});

// POST patient signup
router.post('/signup', [
    body('patient_name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
    body('age').isInt({ min: 1, max: 150 }).withMessage('Age must be between 1 and 150'),
    body('gender').isIn(['Male', 'Female', 'Other']).withMessage('Please select a valid gender'),
    body('contact_number').trim().isLength({ min: 10 }).withMessage('Contact number must be at least 10 digits'),
    body('email').isEmail().normalizeEmail().withMessage('Please enter a valid email'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            req.flash('error', errors.array()[0].msg);
            return res.redirect('/auth/signup');
        }

        const { patient_name, age, gender, contact_number, email, password } = req.body;

        // Check if patient already exists
        const existingPatient = await Patient.findOne({ email });
        if (existingPatient) {
            req.flash('error', 'Email already registered');
            return res.redirect('/auth/signup');
        }

        // Create new patient
        const patient = new Patient({
            patient_name,
            age: parseInt(age),
            gender,
            contact_number,
            email,
            password
        });

        await patient.save();
        req.flash('success', 'Registration successful! Please login.');
        res.redirect('/auth/login');

    } catch (error) {
        console.error('Signup error:', error);
        req.flash('error', 'An error occurred during registration');
        res.redirect('/auth/signup');
    }
});

// POST logout
router.post('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Logout error:', err);
            req.flash('error', 'Error logging out');
            return res.redirect('/');
        }
        res.redirect('/');
    });
});

// GET logout (for convenience)
router.get('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Logout error:', err);
            req.flash('error', 'Error logging out');
            return res.redirect('/');
        }
        res.redirect('/');
    });
});

module.exports = router;
