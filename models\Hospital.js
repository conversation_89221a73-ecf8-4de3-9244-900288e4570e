const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const hospitalSchema = new mongoose.Schema({
    hospital_name: {
        type: String,
        required: [true, 'Hospital name is required'],
        trim: true,
        maxlength: [255, 'Hospital name cannot exceed 255 characters']
    },
    address: {
        type: String,
        required: [true, 'Address is required'],
        trim: true,
        maxlength: [255, 'Address cannot exceed 255 characters']
    },
    zip_code: {
        type: String,
        required: [true, 'Zip code is required'],
        trim: true,
        maxlength: [10, 'Zip code cannot exceed 10 characters']
    },
    contact_number: {
        type: String,
        required: [true, 'Contact number is required'],
        trim: true,
        maxlength: [15, 'Contact number cannot exceed 15 characters']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        unique: true,
        trim: true,
        lowercase: true,
        maxlength: [100, 'Em<PERSON> cannot exceed 100 characters'],
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    doctor_name: {
        type: String,
        required: [true, 'Doctor name is required'],
        trim: true,
        maxlength: [255, 'Doctor name cannot exceed 255 characters']
    },
    doctor_contact_number: {
        type: String,
        required: [true, 'Doctor contact number is required'],
        trim: true,
        maxlength: [15, 'Doctor contact number cannot exceed 15 characters']
    },
    doctor_email: {
        type: String,
        required: [true, 'Doctor email is required'],
        trim: true,
        lowercase: true,
        maxlength: [100, 'Doctor email cannot exceed 100 characters'],
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid doctor email']
    },
    image: {
        type: String,
        default: null
    },
    password: {
        type: String,
        required: [true, 'Password is required'],
        minlength: [6, 'Password must be at least 6 characters long']
    },
    verified: {
        type: Boolean,
        default: false
    },
    current_token: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

// Hash password before saving
hospitalSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();

    try {
        const salt = await bcrypt.genSalt(10);
        this.password = await bcrypt.hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});

// Compare password method
hospitalSchema.methods.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

module.exports = mongoose.model('Hospital', hospitalSchema);
