// Middleware to check if user is logged in
const isLoggedIn = (req, res, next) => {
    if (req.session.isLoggedIn) {
        return next();
    }
    req.flash('error', 'You must be logged in to access this page');
    res.redirect('/auth/login');
};

// Middleware to check if user is a hospital
const isHospital = (req, res, next) => {
    if (req.session.isLoggedIn && req.session.user_role === 'hospital') {
        return next();
    }
    req.flash('error', 'Access denied. Hospital login required.');
    res.redirect('/auth/login');
};

// Middleware to check if user is a patient
const isPatient = (req, res, next) => {
    if (req.session.isLoggedIn && req.session.user_role === 'patient') {
        return next();
    }
    req.flash('error', 'Access denied. Patient login required.');
    res.redirect('/auth/login');
};

// Middleware to check if user is admin
const isAdmin = (req, res, next) => {
    if (req.session.isLoggedIn &&
        req.session.user_role === 'patient' &&
        req.session.email === '<EMAIL>') {
        return next();
    }
    req.flash('error', 'Access denied. Admin privileges required.');
    res.redirect('/');
};

// Middleware to redirect logged in users away from auth pages
const redirectIfLoggedIn = (req, res, next) => {
    if (req.session.isLoggedIn) {
        if (req.session.user_role === 'hospital') {
            return res.redirect('/hospital/profile');
        } else if (req.session.user_role === 'patient') {
            return res.redirect('/patients/profile');
        }
    }
    next();
};

module.exports = {
    isLoggedIn,
    isHospital,
    isPatient,
    isAdmin,
    redirectIfLoggedIn
};
