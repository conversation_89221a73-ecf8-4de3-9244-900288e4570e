const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { body, validationResult } = require('express-validator');
const Patient = require('../models/Patient');
const PatientReport = require('../models/PatientReport');
const Appointment = require('../models/Appointment');
const { isPatient } = require('../middleware/auth');

// Configure multer for report uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/reports/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + '-' + file.originalname);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5242880 // 5MB
    },
    fileFilter: function (req, file, cb) {
        const allowedTypes = /pdf|doc|docx|jpg|jpeg|png/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype) || file.mimetype === 'application/pdf';

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Only PDF, DOC, DOCX, and image files are allowed'));
        }
    }
});

// GET patient profile page
router.get('/profile', isPatient, async (req, res) => {
    try {
        const patient = await Patient.findOne({ email: req.session.email }).select('-password');
        if (!patient) {
            req.flash('error', 'Patient not found');
            return res.redirect('/auth/logout');
        }

        // Get patient reports
        const reports = await PatientReport.find({ patient_id: patient._id })
            .sort({ report_date: -1 });

        // Get patient appointments
        const appointments = await Appointment.find({ email: patient.email })
            .populate('hospital_id', 'hospital_name')
            .sort({ createdAt: -1 });

        res.render('patients/profile', {
            title: 'Patient Profile',
            patient,
            reports,
            appointments,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching patient profile:', error);
        req.flash('error', 'Error loading profile');
        res.redirect('/');
    }
});

// GET patient appointments
router.get('/appointments', isPatient, async (req, res) => {
    try {
        const patient = await Patient.findOne({ email: req.session.email });
        if (!patient) {
            req.flash('error', 'Patient not found');
            return res.redirect('/auth/logout');
        }

        const appointments = await Appointment.find({ email: patient.email })
            .populate('hospital_id', 'hospital_name contact_number')
            .sort({ createdAt: -1 });

        res.render('patients/appointments', {
            title: 'My Appointments',
            appointments,
            patient,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching appointments:', error);
        req.flash('error', 'Error loading appointments');
        res.redirect('/patients/profile');
    }
});

// GET patient reports
router.get('/reports', isPatient, async (req, res) => {
    try {
        const patient = await Patient.findOne({ email: req.session.email });
        if (!patient) {
            req.flash('error', 'Patient not found');
            return res.redirect('/auth/logout');
        }

        const reports = await PatientReport.find({ patient_id: patient._id })
            .sort({ report_date: -1 });

        res.render('patients/reports', {
            title: 'My Reports',
            reports,
            patient,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error fetching reports:', error);
        req.flash('error', 'Error loading reports');
        res.redirect('/patients/profile');
    }
});

// POST upload report
router.post('/reports/upload', isPatient, upload.single('report_file'), [
    body('report_title').trim().isLength({ min: 2 }).withMessage('Report title must be at least 2 characters'),
    body('report_description').optional().trim().isLength({ max: 1000 }).withMessage('Description cannot exceed 1000 characters')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            req.flash('error', errors.array()[0].msg);
            return res.redirect('/patients/reports');
        }

        if (!req.file) {
            req.flash('error', 'Please select a file to upload');
            return res.redirect('/patients/reports');
        }

        const patient = await Patient.findOne({ email: req.session.email });
        if (!patient) {
            req.flash('error', 'Patient not found');
            return res.redirect('/auth/logout');
        }

        const { report_title, report_description } = req.body;

        const report = new PatientReport({
            patient_id: patient._id,
            report_file: req.file.path,
            report_title,
            report_description,
            report_date: new Date()
        });

        await report.save();
        req.flash('success', 'Report uploaded successfully');
        res.redirect('/patients/reports');

    } catch (error) {
        console.error('Error uploading report:', error);
        req.flash('error', 'Error uploading report');
        res.redirect('/patients/reports');
    }
});

module.exports = router;
