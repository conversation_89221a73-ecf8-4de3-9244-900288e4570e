*{
    margin-top: 0;
}

:root {
    --nav-bg: #1e1e1e;
    --nav-col-def: #6c757d;
    --ironblack: #373737;
  }
  
body{
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.flex {
    display: flex;
    flex-direction: row;
}

.coverImg {
    width: 800px;
    height: 600px;
    margin: 0px 20px;
    border-radius: 10px;
    object-fit: contain;
}

.details{
    height: 370px;
    width: 35vw;
    overflow-y: auto;
  }

.middle{
    display: flex;
    flex-direction: column;
    justify-content:center;
}

.box-2 {
    width: 40vw;
  }

  .buy {
    border: 1px solid #aeb2b6;
    border-radius: 20px;
    padding: 15px 20px;
    padding-top: 25px;
    width: 17vw;
    background-color: #dddddd;
    box-shadow: 0px 0px 4.5px 0.5px;
  }

  .dibba {
    position: sticky;
    z-index: 1;
  }

  .out:hover {
    transform: scale(1.05);
    transition-property: transform;
    transition-duration: 80ms;
    transition-timing-function: ease-in-out;
  }

.container{
    flex: 1;
}
.maintoggle{
    margin-top: 10rem;
}
.navbar{
    height:5rem;
    z-index: 1 !important;
    opacity: 1!important;
    color: white !important;
}

.fa-compass{
    color: #fe424d;
    font-size: 2rem;
}
.nav-link{
    color: #222222 !important;
}

.f-info-links a{
    text-decoration: none;
    color: #222222;
}
.f-info-links a:hover{
    text-decoration: underline;
   
}
.f-info{
    text-align: center;
    background-color: #ebebeb;
    display: flex;
    height: 8rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: space-evenly;
}
.f-info-socials{
    width:100%;
    font-size: 1.5rem;
}
.f-info-socials i{
 margin-right: 1.2rem;
}
.f-info-links{
    width:100%;
}

.listing-card{
    border:none !important;
    margin-bottom: 2rem;
}

.card-img-top{
    border-radius: 1rem !important;
    width: 100% !important;
    object-fit: cover !important;
}

.card-body{
    padding: 0 !important;
}

.listing-link{
    text-decoration: none;
}

.card-img-overlay{
    opacity: 0;
}
.card-img-overlay:hover{
    opacity: 0.2;
    background-color: white;
}

.add-btn{
    background-color: #fe424d !important; 
    border: none !important;
}

.show-img{
    height: 30vh;
}

.imp-btn{
    display: flex;
}

.ogimg{
    width: 300px;
}

@media(max-width:480px){
    .ogimg{
        width:220px;
        border-radius: 1rem ;

    }
}

